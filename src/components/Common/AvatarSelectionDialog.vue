<template>
  <div class="dialog-overlay">
    <div class="dialog-container">
      <div class="dialog-header">
        <div class="dialog-title">选择头像</div>
        <div class="dialog-close" @click="$emit('close')">
          <DeleteIcon :size="24" color="var(--primary-color)" />
        </div>
      </div>
      <div class="dialog-content">
        <!-- 默认头像选择区域 -->
        <div class="avatar-section">
          <!-- 分页指示器 -->
          <div class="page-indicators">
            <div
              v-for="(_, pageIndex) in avatarPages"
              :key="pageIndex"
              class="page-indicator"
              :class="{ active: currentPage === pageIndex }"
              @click="goToPage(pageIndex)"
            >
              {{ pageIndex + 1 }}
            </div>
          </div>

          <!-- 滑动容器 -->
          <div class="avatar-slider-container">
            <div class="avatar-slider" :style="{ transform: `translateX(-${currentPage * 100}%)` }">
              <div v-for="(page, pageIndex) in avatarPages" :key="pageIndex" class="avatar-page">
                <div class="avatar-grid">
                  <div
                    v-for="(avatar, index) in page"
                    :key="avatar.id"
                    class="avatar-option"
                    @click="selectAvatar(avatar.id)"
                  >
                    <img :src="avatar.src" :alt="`默认头像${index + 1}`" class="avatar-preview" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 左右滑动按钮 -->
          <div class="slider-controls">
            <button class="slider-btn prev-btn" :disabled="currentPage === 0" @click="prevPage">‹</button>
            <button class="slider-btn next-btn" :disabled="currentPage === avatarPages.length - 1" @click="nextPage">
              ›
            </button>
          </div>
        </div>

        <!-- 上传头像区域 -->
        <div class="upload-section">
          <div class="section-title">上传自定义头像</div>
          <div class="upload-button" @click="$emit('upload-avatar')">
            <i class="iconfont icon-camera upload-icon"></i>
            <span class="upload-text">点击上传头像</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getDefaultAvatarsByPages } from '@/utils/avatarUtils';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

// Emits定义
const emit = defineEmits<{
  close: [];
  'select-avatar': [avatarUrl: string];
  'upload-avatar': [];
}>();

// 分页头像列表
const avatarPages = getDefaultAvatarsByPages();

// 当前页面索引
const currentPage = ref(0);

// 选择头像
const selectAvatar = (avatarId: string) => {
  emit('select-avatar', avatarId);
};

// 切换到指定页面
const goToPage = (pageIndex: number) => {
  currentPage.value = pageIndex;
};

// 上一页
const prevPage = () => {
  if (currentPage.value > 0) {
    currentPage.value--;
  }
};

// 下一页
const nextPage = () => {
  if (currentPage.value < avatarPages.length - 1) {
    currentPage.value++;
  }
};
</script>

<style lang="scss" scoped>
// 对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: var(--bg-glass-popup);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 600px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 40px; // 增加8px (原来32px)
    font-weight: 600;
  }

  .dialog-close {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.avatar-section,
.upload-section {
  border: 2px solid var(--border-accent);
  border-radius: 16px;
  padding: 24px;
  background: var(--primary-color-light);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
}

.section-title {
  color: var(--accent-color);
  font-size: 32px; // 增加8px (原来24px)
  font-weight: 600;
  margin-bottom: 20px;
}

// 分页指示器样式
.page-indicators {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.page-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.6);
  font-size: 18px; // 增加4px (原来14px)
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
  }

  &.active {
    background: var(--accent-color);
    color: white;
    box-shadow: var(--shadow-accent);
  }
}

// 滑动容器样式
.avatar-slider-container {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  margin: 0 20px; // 为左右按钮留出空间
}

.avatar-slider {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.avatar-page {
  flex: 0 0 100%;
  width: 100%;
}

.avatar-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 8px;
}

.avatar-option {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  justify-self: center;


}

// 滑动控制按钮样式
.slider-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  z-index: 10;
}

.slider-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 24px; // 增加4px (原来20px)
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  pointer-events: auto;
  backdrop-filter: blur(10px);

  &:hover:not(:disabled) {
    background: rgba(0, 0, 0, 0.8);
    border-color: var(--accent-color);
    box-shadow: var(--shadow-accent);
    transform: scale(1.1);
  }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }
}

.prev-btn {
  margin-left: -20px;
}

.next-btn {
  margin-right: -20px;
}

.avatar-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.upload-section {
  text-align: center;
}

.upload-button {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 24px 32px;
  background: var(--primary-color-light);
  border: 2px solid var(--primary-color);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--primary-color);


}

.upload-icon {
  font-size: 40px; // 增加8px (原来32px)
}

.upload-text {
  font-size: 28px; // 增加8px (原来20px)
  font-weight: 600;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
